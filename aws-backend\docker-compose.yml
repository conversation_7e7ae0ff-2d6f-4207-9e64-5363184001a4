version: '3.8'

services:
  localstack:
    container_name: "${LOCALSTACK_DOCKER_NAME:-gameflex-localstack}"
    image: localstack/localstack-pro  # required for Pro
    ports:
      - "127.0.0.1:4566:4566"            # LocalStack Gateway
      - "127.0.0.1:4510-4559:4510-4559"  # external services port range
      - "127.0.0.1:443:443"              # LocalStack HTTPS Gateway (Pro)
      - "45660:4566"                     # Keep existing port mapping for compatibility
    env_file:
      - .env
    environment:
      # Activate LocalStack Pro: https://docs.localstack.cloud/getting-started/auth-token/
      - LOCALSTACK_AUTH_TOKEN=${LOCALSTACK_AUTH_TOKEN:?}  # required for Pro
      # LocalStack configuration: https://docs.localstack.cloud/references/configuration/
      - DEBUG=${DEBUG:-1}
      - PERSISTENCE=${PERSISTENCE:-1}
      - LAMBDA_EXECUTOR=docker
      - DOCKER_HOST=unix:///var/run/docker.sock
      - HOST_TMP_FOLDER=${TMPDIR:-/tmp}/localstack

      # AWS Configuration
      - AWS_DEFAULT_REGION=us-east-1
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test

      # Enable specific services
      - SERVICES=cognito-idp,cognito-identity,rds,lambda,apigateway,s3,cloudformation,iam,sts,logs

      # Database configuration
      - RDS_ENGINE=postgres
      - RDS_PORT=5432

      # S3 configuration
      - S3_SKIP_SIGNATURE_VALIDATION=1

      # Lambda configuration
      - LAMBDA_RUNTIME_ENVIRONMENT_TIMEOUT=60
      - LAMBDA_REMOVE_CONTAINERS=true

      # API Gateway configuration
      - GATEWAY_LISTEN=0.0.0.0:4566

    volumes:
      - "${LOCALSTACK_VOLUME_DIR:-./volume}:/var/lib/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"
      - "./init:/etc/localstack/init/ready.d"
      - "./lambda-functions:/opt/lambda-functions"
      - "./cloudformation:/opt/cloudformation"
    networks:
      - gameflex-aws
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4566/_localstack/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # PostgreSQL for Aurora Serverless emulation (LocalStack Community)
  postgres:
    container_name: gameflex-postgres
    image: postgres:15-alpine
    restart: unless-stopped
    ports:
      - "54320:5432"  # High port number to avoid conflicts
    environment:
      POSTGRES_DB: gameflex
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: gameflex_password
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256 --auth-local=scram-sha-256"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - gameflex-aws
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d gameflex"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and session management
  redis:
    container_name: gameflex-redis
    image: redis:7-alpine
    restart: unless-stopped
    ports:
      - "63800:6379"  # High port number to avoid conflicts
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - gameflex-aws
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  gameflex-aws:
    driver: bridge
